"use client";

import { useEffect } from 'react';

/**
 * Hook to handle smooth theme transitions and prevent flash of unstyled content
 */
export function useThemeTransition() {
  useEffect(() => {
    // Remove no-transition class after initial load to enable smooth transitions
    const timer = setTimeout(() => {
      document.body.classList.remove('no-transition');
    }, 100);

    // Add no-transition class on page load to prevent flash
    document.body.classList.add('no-transition');

    return () => clearTimeout(timer);
  }, []);

  useEffect(() => {
    // Add support for reduced motion preference
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    
    const handleMotionChange = (e: MediaQueryListEvent) => {
      if (e.matches) {
        document.documentElement.style.setProperty('--transition-duration', '0s');
      } else {
        document.documentElement.style.removeProperty('--transition-duration');
      }
    };

    handleMotionChange(mediaQuery as any);
    mediaQuery.addEventListener('change', handleMotionChange);

    return () => mediaQuery.removeEventListener('change', handleMotionChange);
  }, []);
}

/**
 * Utility function to create a smooth circular transition effect with contracting animation
 */
export function createCircularTransition(
  event: React.MouseEvent,
  callback: () => void,
  duration: number = 600
) {
  if (!('startViewTransition' in document)) {
    callback();
    return;
  }

  const rect = event.currentTarget.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;
  const endRadius = Math.hypot(
    Math.max(x, window.innerWidth - x),
    Math.max(y, window.innerHeight - y)
  );

  // Determine if switching to dark mode (contracting) or light mode (expanding)
  const isDarkMode = document.documentElement.classList.contains('dark');
  const switchingToDark = !isDarkMode;

  const transition = (document as any).startViewTransition(callback);

  transition.ready.then(() => {
    if (switchingToDark) {
      // Contracting effect for light to dark
      const clipPath = [
        `circle(${endRadius}px at ${x}px ${y}px)`,
        `circle(0px at ${x}px ${y}px)`
      ];

      document.documentElement.animate(
        {
          clipPath: clipPath,
        },
        {
          duration,
          easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
          pseudoElement: '::view-transition-old(root)',
        }
      );

      // New dark theme expands from center
      document.documentElement.animate(
        {
          clipPath: [
            `circle(0px at ${x}px ${y}px)`,
            `circle(${endRadius}px at ${x}px ${y}px)`
          ],
        },
        {
          duration,
          easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
          pseudoElement: '::view-transition-new(root)',
        }
      );
    } else {
      // Expanding effect for dark to light (original behavior)
      const clipPath = [
        `circle(0px at ${x}px ${y}px)`,
        `circle(${endRadius}px at ${x}px ${y}px)`
      ];

      document.documentElement.animate(
        {
          clipPath: clipPath,
        },
        {
          duration,
          easing: 'cubic-bezier(0.4, 0, 0.2, 1)',
          pseudoElement: '::view-transition-new(root)',
        }
      );
    }
  });
}

/**
 * Enhanced circular transition with more control over animation direction
 */
export function createContractingTransition(
  event: React.MouseEvent,
  callback: () => void,
  duration: number = 600
) {
  if (!('startViewTransition' in document)) {
    callback();
    return;
  }

  const rect = event.currentTarget.getBoundingClientRect();
  const x = rect.left + rect.width / 2;
  const y = rect.top + rect.height / 2;
  const endRadius = Math.hypot(
    Math.max(x, window.innerWidth - x),
    Math.max(y, window.innerHeight - y)
  );

  const transition = (document as any).startViewTransition(callback);

  transition.ready.then(() => {
    // Always contract from full size to center point
    const clipPath = [
      `circle(${endRadius}px at ${x}px ${y}px)`,
      `circle(0px at ${x}px ${y}px)`
    ];

    document.documentElement.animate(
      {
        clipPath: clipPath,
      },
      {
        duration: duration * 0.6, // Faster contraction
        easing: 'cubic-bezier(0.6, 0, 0.4, 1)',
        pseudoElement: '::view-transition-old(root)',
      }
    );

    // New theme expands from center
    document.documentElement.animate(
      {
        clipPath: [
          `circle(0px at ${x}px ${y}px)`,
          `circle(${endRadius}px at ${x}px ${y}px)`
        ],
      },
      {
        duration: duration * 0.8, // Slower expansion
        delay: duration * 0.2, // Slight delay for smoother transition
        easing: 'cubic-bezier(0.2, 0, 0.4, 1)',
        pseudoElement: '::view-transition-new(root)',
      }
    );
  });
}
