"use client";

import React from "react";
import { useTheme } from "@/contexts/ThemeContext";
import { AnimatedThemeIcon } from "./AnimatedThemeIcon";

interface ThemeToggleProps {
  className?: string;
  size?: "sm" | "md" | "lg";
  variant?: "button" | "icon";
}

export function ThemeToggle({
  className = "",
  size = "md",
  variant = "icon"
}: ThemeToggleProps) {
  const { theme, resolvedTheme, toggleTheme, setTheme } = useTheme();

  const handleToggle = (event: React.MouseEvent) => {
    const button = event.currentTarget as HTMLButtonElement;
    const switchingToDark = resolvedTheme === 'light';

    // Add appropriate CSS class for animation
    if (switchingToDark) {
      button.classList.add('switching-to-dark');
      setTimeout(() => button.classList.remove('switching-to-dark'), 600);
    } else {
      button.classList.add('switching-to-light');
      setTimeout(() => button.classList.remove('switching-to-light'), 600);
    }

    toggleTheme(event);
  };

  const sizeClasses = {
    sm: "w-8 h-8",
    md: "w-12 h-12", 
    lg: "w-16 h-16"
  };

  const iconSizes = {
    sm: { width: 20, height: 20 },
    md: { width: 28, height: 28 },
    lg: { width: 36, height: 36 }
  };

  if (variant === "button") {
    return (
      <button
        onClick={handleToggle}
        className={`
          theme-toggle-button
          inline-flex items-center justify-center gap-2 px-4 py-2
          rounded-lg bg-white/50 dark:bg-[#232435]/50
          border border-gray-200 dark:border-white/12
          text-gray-800 dark:text-white
          hover:bg-white/70 dark:hover:bg-[#232435]/70
          transition-all duration-300 ease-in-out
          hover:scale-105 active:scale-95
          backdrop-blur-sm
          ${className}
        `}
        aria-label={`Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode`}
      >
        <AnimatedThemeIcon width={iconSizes.sm.width} height={iconSizes.sm.height} />
        <span className="text-sm font-medium">
          {resolvedTheme === 'dark' ? 'Light' : 'Dark'} Mode
        </span>
      </button>
    );
  }

  return (
    <button
      onClick={handleToggle}
      className={`
        theme-toggle-button
        ${sizeClasses[size]}
        rounded-full bg-white/50 dark:bg-[#232435]/50
        border border-gray-200 dark:border-white/12
        flex items-center justify-center cursor-pointer
        hover:bg-white/70 dark:hover:bg-[#232435]/70
        transition-all duration-300 ease-in-out
        hover:scale-110 active:scale-95
        hover:shadow-lg hover:shadow-gray-200/50 dark:hover:shadow-gray-800/50
        backdrop-blur-sm
        ${className}
      `}
      aria-label={`Switch to ${resolvedTheme === 'dark' ? 'light' : 'dark'} mode`}
      title={`Current: ${theme} mode (${resolvedTheme})`}
    >
      <div className="transition-transform duration-300 ease-in-out hover:rotate-12">
        <AnimatedThemeIcon
          width={iconSizes[size].width}
          height={iconSizes[size].height}
          className="text-gray-600 dark:text-gray-300"
        />
      </div>
    </button>
  );
}

// Theme selector dropdown component for more advanced theme switching
export function ThemeSelector({ className = "" }: { className?: string }) {
  const { theme, setTheme } = useTheme();

  return (
    <select
      value={theme}
      onChange={(e) => setTheme(e.target.value as 'light' | 'dark' | 'system')}
      className={`
        px-3 py-2 rounded-lg 
        bg-white/50 dark:bg-[#232435]/50 
        border border-gray-200 dark:border-white/12
        text-gray-800 dark:text-white
        text-sm font-medium
        cursor-pointer
        transition-all duration-200
        ${className}
      `}
      aria-label="Select theme"
    >
      <option value="light">Light</option>
      <option value="dark">Dark</option>
      <option value="system">System</option>
    </select>
  );
}

// Hook for components that need theme information
export function useThemeInfo() {
  const { theme, resolvedTheme } = useTheme();
  
  return {
    theme,
    resolvedTheme,
    isDark: resolvedTheme === 'dark',
    isLight: resolvedTheme === 'light',
    isSystem: theme === 'system'
  };
}